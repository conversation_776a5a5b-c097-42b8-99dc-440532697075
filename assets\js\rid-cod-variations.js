/**
 * RIDCOD Variations Handler - Enhanced Integration with WooCommerce
 * This version integrates directly with WooCommerce variation events for better compatibility
 */
jQuery(document).ready(function($) {
    console.log('RIDCOD Variations: Initializing Enhanced WooCommerce Handler');

    // Get variation data from global variable (set in PHP)
    var variationsData = (typeof rid_cod_variations !== 'undefined') ? rid_cod_variations : [];

    // Only proceed if we have variation elements and data
    if ($('.rid-cod-variations').length === 0 || variationsData.length === 0) {
        console.log('RIDCOD Variations: No variations to handle');
        return;
    }

    console.log('RIDCOD Variations: Found ' + variationsData.length + ' variations');

    // Check if dropdown variations mode is enabled
    var useDropdownVariations = (typeof rid_cod_params !== 'undefined' && rid_cod_params.use_dropdown_variations) || false;
    if (useDropdownVariations) {
        $('body').addClass('rid-cod-use-dropdown-variations');
        console.log('RIDCOD Variations: Dropdown mode enabled');
    }

    // Cache DOM elements
    var $form = $('#rid-cod-form');
    var $variationSelects = $('.rid-cod-variations select.rid-cod-variation-select');
    var $variationIdInput = $('#rid-cod-variation-id');
    var $variationPriceInput = $('#rid-cod-variation-price');
    var $productPrice = $('#rid-cod-product-price');
    var $summaryVariationDetails = $('#rid-cod-summary-variation-details');
    var $totalPrice = $('#rid-cod-total-price'); // For updating total if it exists
    var $productImage = $('.woocommerce-product-gallery__image img, .wp-post-image').first();
    var originalImageSrc = $productImage.attr('src');

    // Connect to WooCommerce variations container - now a div, not a form
    var $wooVariationsForm = $('.rid-cod-variations .variations_form');
    if ($wooVariationsForm.length > 0) {
        console.log('RIDCOD: WooCommerce variations container found, connecting events');

        // Since it's now a div, we need to manually trigger WooCommerce events
        // We'll handle variation logic ourselves and sync with WooCommerce when needed
    } else {
        console.warn('RIDCOD: No WooCommerce variations container found, using custom handlers');
        // We'll need to rely on our own handlers
        $wooVariationsForm = $('.rid-cod-variations');
    }
    
    // NEW: Function to decode URL-encoded attribute values (for Arabic text)
    function decodeAttributeValue(value) {
        if (!value) return value;
        
        try {
            var decoded = value;
            
            // Keep decoding while there are still encoded characters
            while (decoded.indexOf('%') !== -1 && decoded !== decodeURIComponent(decoded)) {
                decoded = decodeURIComponent(decoded);
            }
            
            // Remove common attribute prefixes
            var prefixes = ['pa_', 'attribute_pa_', 'attribute_'];
            for (var i = 0; i < prefixes.length; i++) {
                if (decoded.indexOf(prefixes[i]) === 0) {
                    decoded = decoded.substring(prefixes[i].length);
                    break;
                }
            }
            
            // Log if decoding was necessary
            if (decoded !== value) {
                console.log('RIDCOD: Decoded attribute value:', value, '->', decoded);
            }
            
            return decoded.trim();
        } catch (e) {
            console.warn('RIDCOD: Failed to decode attribute value:', value, e);
            return value;
        }
    }
    
    // Function to get clean attribute value from element
    function getCleanAttributeValue($element) {
        // First try to get decoded value if available
        var decodedValue = $element.data('decoded-value');
        if (decodedValue) {
            return decodedValue;
        }
        
        // Otherwise decode the regular value
        var rawValue = $element.data('value');
        return decodeAttributeValue(rawValue);
    }
    // Format price with currency symbol
    function formatPrice(price) {
        // Try to use the main formatPrice function if available, otherwise fallback
        if (typeof window.formatPrice === 'function') {
            return window.formatPrice(price);
        }
        
        // Fallback: Use country-specific currency if rid_cod_params is available
        var currentCountry = 'DZ'; // Default
        var currencySymbol = 'د.ج'; // Default
        
        if (typeof rid_cod_params !== 'undefined') {
            currentCountry = rid_cod_params.current_country || 'DZ';
            if (rid_cod_params.country_data && rid_cod_params.country_data[currentCountry]) {
                currencySymbol = rid_cod_params.country_data[currentCountry].currency_symbol || 'د.ج';
            }
        }
        
        var formattedPrice = parseFloat(price).toFixed(2).replace('.', ',');
        formattedPrice = formattedPrice.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
        
        return currencySymbol + ' ' + formattedPrice;
    }
    function getAvailableAttributeValues(targetAttribute) {
        var availableValues = [];
        var currentSelections = {};
        
        // Get current selections for all other attributes (excluding target attribute)
        $variationSelects.each(function() {
            var attrName = $(this).attr('name');
            var attrValue = $(this).val();
            
            // Skip the target attribute and empty values
            if (attrName !== targetAttribute && attrValue) {
                currentSelections[attrName] = attrValue;
            }
        });
        
        // IMPORTANT: Always allow all values for the first attribute (primary selector)
        var $firstSelect = $variationSelects.first();
        var firstAttributeName = $firstSelect.attr('name');
        
        if (targetAttribute === firstAttributeName) {
            // First attribute should always show all available values
            for (var i = 0; i < variationsData.length; i++) {
                var variation = variationsData[i];
                var cleanTargetAttr = targetAttribute.replace('attribute_', '');
                var targetValue = variation.attributes[targetAttribute] || variation.attributes['attribute_' + cleanTargetAttr];
                
                if (targetValue && targetValue !== '' && availableValues.indexOf(targetValue) === -1) {
                    availableValues.push(targetValue);
                }
            }
            return availableValues;
        }
        
        // For other attributes, if no other attributes are selected, all values should be available
        if (Object.keys(currentSelections).length === 0) {
            for (var i = 0; i < variationsData.length; i++) {
                var variation = variationsData[i];
                var cleanTargetAttr = targetAttribute.replace('attribute_', '');
                var targetValue = variation.attributes[targetAttribute] || variation.attributes['attribute_' + cleanTargetAttr];
                
                if (targetValue && targetValue !== '' && availableValues.indexOf(targetValue) === -1) {
                    availableValues.push(targetValue);
                }
            }
            return availableValues;
        }
        
        // Find all variations that match current selections (excluding target attribute)
        for (var i = 0; i < variationsData.length; i++) {
            var variation = variationsData[i];
            var isCompatible = true;
            
            // Check if this variation matches all current selections (with decoding support)
            for (var attrName in currentSelections) {
                var cleanAttrName = attrName.replace('attribute_', '');
                var selectedValue = currentSelections[attrName];
                var variationAttr = variation.attributes[attrName] || variation.attributes['attribute_' + cleanAttrName];
                
                // Decode both values for comparison
                var decodedSelectedValue = decodeAttributeValue(selectedValue);
                var decodedVariationAttr = decodeAttributeValue(variationAttr);
                
                // Handle "any" values - if variation has empty value, it means "any" value is acceptable
                if (variationAttr && variationAttr !== '' && 
                    variationAttr !== selectedValue && 
                    decodedVariationAttr !== decodedSelectedValue &&
                    decodedVariationAttr !== selectedValue &&
                    variationAttr !== decodedSelectedValue) {
                    isCompatible = false;
                    break;
                }
            }
            
            // If compatible, add the target attribute value to available list
            if (isCompatible) {
                var cleanTargetAttr = targetAttribute.replace('attribute_', '');
                var targetValue = variation.attributes[targetAttribute] || variation.attributes['attribute_' + cleanTargetAttr];
                
                if (targetValue && targetValue !== '' && availableValues.indexOf(targetValue) === -1) {
                    availableValues.push(targetValue);
                }
            }
        }
        
        console.log('RIDCOD: Available values for', targetAttribute, ':', availableValues, 'Current selections:', currentSelections);
        return availableValues;
    }
    
    // NEW: Function to update available options based on current selections
    function updateAvailableOptions() {
        console.log('RIDCOD: Updating available options based on current selections');
        
        // Get the first attribute (primary selector) - should always be available
        var $firstSelect = $variationSelects.first();
        var firstAttributeName = $firstSelect.attr('name');
        
        console.log('RIDCOD: First attribute (always enabled):', firstAttributeName);
        
        $variationSelects.each(function() {
            var $select = $(this);
            var attributeName = $select.attr('name');
            var currentValue = $select.val();
            var $boxesContainer = $('.variation-boxes[data-attribute="' + attributeName + '"]');
            
            // Special handling for the first attribute - always keep it enabled
            var isFirstAttribute = (attributeName === firstAttributeName);
            
            console.log('RIDCOD: Processing attribute:', attributeName, 'Is first:', isFirstAttribute, 'Current value:', currentValue);
            
            // Get available values (for first attribute, this will return all values)
            var availableValues = getAvailableAttributeValues(attributeName);
            console.log('RIDCOD: Available values for', attributeName, ':', availableValues);
            
            // First, check if current selection is still valid (but NEVER for first attribute)
            if (!isFirstAttribute && currentValue && availableValues.length > 0 && availableValues.indexOf(currentValue) === -1) {
                // Current selection is invalid - clear it immediately
                $select.val('');
                $boxesContainer.find('.variation-option[data-value="' + currentValue + '"]').removeClass('selected');
                console.log('RIDCOD: Auto-cleared incompatible selection:', currentValue, 'for attribute:', attributeName);
                
                // Show feedback message
                var $invalidOption = $boxesContainer.find('.variation-option[data-value="' + currentValue + '"]');
                if ($invalidOption.length) {
                    showSelectionClearedMessage($invalidOption, attributeName);
                }
                
                // Update currentValue since we cleared it
                currentValue = '';
            }
            
            // Now update visibility/availability of all options
            $boxesContainer.find('.variation-option').each(function() {
                var $option = $(this);
                var optionValue = $option.data('value');
                var wasDisabled = $option.hasClass('disabled');
                
                // CRITICAL: First attribute is NEVER disabled - all options stay available
                var shouldBeDisabled = false;
                if (!isFirstAttribute) {
                    // For secondary attributes only, check restrictions
                    shouldBeDisabled = availableValues.length > 0 && availableValues.indexOf(optionValue) === -1;
                }
                
                console.log('RIDCOD: Option', optionValue, 'in attribute', attributeName, 'should be disabled:', shouldBeDisabled, 'isFirst:', isFirstAttribute);
                
                if (shouldBeDisabled && !wasDisabled) {
                    // Option is becoming disabled
                    $option.addClass('becoming-disabled');
                    setTimeout(function() {
                        $option.removeClass('becoming-disabled').addClass('disabled unavailable');
                    }, 150);
                } else if (!shouldBeDisabled && wasDisabled) {
                    // Option is becoming enabled
                    $option.addClass('becoming-enabled');
                    setTimeout(function() {
                        $option.removeClass('becoming-enabled disabled unavailable');
                    }, 150);
                } else if (isFirstAttribute && wasDisabled) {
                    // Force enable first attribute options that might have been disabled
                    $option.removeClass('disabled unavailable becoming-disabled');
                    console.log('RIDCOD: Force-enabled first attribute option:', optionValue);
                }
            });
        });
        
        // Update the hidden selects too
        $variationSelects.each(function() {
            var $select = $(this);
            var attributeName = $select.attr('name');
            var availableValues = getAvailableAttributeValues(attributeName);
            var currentValue = $select.val();
            var isFirstAttribute = (attributeName === firstAttributeName);
            
            console.log('RIDCOD: Updating hidden select for:', attributeName, 'Is first:', isFirstAttribute);
            
            // Check if current value is still valid (but NEVER for first attribute)
            if (!isFirstAttribute && currentValue && availableValues.length > 0 && availableValues.indexOf(currentValue) === -1) {
                $select.val(''); // Clear invalid selection
                console.log('RIDCOD: Cleared invalid selection in hidden select:', currentValue);
            }
            
            // Disable/enable options in the hidden select (but NEVER disable first attribute options)
            $select.find('option').each(function() {
                var $option = $(this);
                var optionValue = $option.val();
                
                if (!isFirstAttribute && optionValue && availableValues.length > 0 && availableValues.indexOf(optionValue) === -1) {
                    $option.prop('disabled', true);
                } else {
                    $option.prop('disabled', false);
                    // Force enable first attribute options
                    if (isFirstAttribute && $option.prop('disabled')) {
                        console.log('RIDCOD: Force-enabled first attribute option in select:', optionValue);
                    }
                }
            });
        });
        
        // Force ensure first attribute is never disabled
        ensureFirstAttributeAlwaysEnabled();
    }
    
    // NEW: Ensure the first attribute always remains enabled
    function ensureFirstAttributeAlwaysEnabled() {
        var $firstSelect = $variationSelects.first();
        var firstAttributeName = $firstSelect.attr('name');
        var $firstBoxesContainer = $('.variation-boxes[data-attribute="' + firstAttributeName + '"]');
        
        // Remove any disabled classes from first attribute options
        $firstBoxesContainer.find('.variation-option').removeClass('disabled unavailable');
        
        // Enable all options in the first attribute select
        $firstSelect.find('option').prop('disabled', false);
        
        console.log('RIDCOD: Ensured first attribute is fully enabled:', firstAttributeName);
    }
    
    // NEW: Show a subtle message when a selection is cleared
    function showSelectionClearedMessage($option, attributeName) {
        // Don't show multiple messages for the same element
        if ($option.find('.rid-selection-cleared-message').length > 0) {
            return;
        }
        
        // Create a temporary tooltip-like message
        var $message = $('<div class="rid-selection-cleared-message">تم إلغاء الاختيار</div>');
        $option.append($message);
        
        // Position and style the message
        $message.css({
            position: 'absolute',
            top: '-35px',
            left: '50%',
            transform: 'translateX(-50%)',
            background: 'rgba(220, 53, 69, 0.95)',
            color: 'white',
            padding: '6px 10px',
            borderRadius: '6px',
            fontSize: '11px',
            whiteSpace: 'nowrap',
            zIndex: 1000,
            opacity: 0,
            boxShadow: '0 2px 8px rgba(0,0,0,0.2)',
            border: '1px solid rgba(255,255,255,0.2)'
        });
        
        // Animate in and out
        $message.animate({opacity: 1}, 300);
        setTimeout(function() {
            $message.animate({opacity: 0}, 300, function() {
                $message.remove();
            });
        }, 2000);
    }
    
    // NEW: Function to reset incompatible selections when an attribute changes
    function resetIncompatibleSelections(changedAttribute) {
        var hasChanges = false;
        var clearedAttributes = [];
        
        // Get the first attribute (primary selector) - should never be reset
        var $firstSelect = $variationSelects.first();
        var firstAttributeName = $firstSelect.attr('name');
        
        console.log('RIDCOD: Checking incompatible selections. Changed attribute:', changedAttribute, 'First attribute (protected):', firstAttributeName);
        
        $variationSelects.each(function() {
            var $select = $(this);
            var attributeName = $select.attr('name');
            var currentValue = $select.val();
            
            // Skip the attribute that just changed
            if (attributeName === changedAttribute) {
                return;
            }
            
            // CRITICAL: Never reset the first attribute (primary selector)
            var isFirstAttribute = (attributeName === firstAttributeName);
            if (isFirstAttribute) {
                console.log('RIDCOD: Skipping first attribute reset for:', attributeName);
                return;
            }
            
            if (currentValue) {
                var availableValues = getAvailableAttributeValues(attributeName);
                
                // If current value is not available anymore, reset it
                // But only if there are restrictions (availableValues.length > 0)
                if (availableValues.length > 0 && availableValues.indexOf(currentValue) === -1) {
                    $select.val('');
                    
                    // Remove visual selection from boxes
                    var $boxesContainer = $('.variation-boxes[data-attribute="' + attributeName + '"]');
                    $boxesContainer.find('.variation-option').removeClass('selected');
                    
                    // Get attribute label for user feedback
                    var attributeLabel = $select.closest('.variation-row').find('label').text().replace(':', '').trim();
                    clearedAttributes.push(attributeLabel || attributeName);
                    
                    hasChanges = true;
                    console.log('RIDCOD: Reset incompatible secondary attribute:', attributeName, 'value:', currentValue);
                    
                    // Show feedback message
                    var $clearedOption = $boxesContainer.find('.variation-option[data-value="' + currentValue + '"]');
                    if ($clearedOption.length) {
                        showSelectionClearedMessage($clearedOption, attributeName);
                    }
                }
            }
        });
        
        // Show global message if any attributes were cleared
        if (hasChanges && clearedAttributes.length > 0) {
            var message = 'تم إلغاء اختيار: ' + clearedAttributes.join(', ') + ' (غير متوافق مع الاختيار الجديد)';
            showGlobalSelectionMessage(message, 'warning');
        }
        
        return hasChanges;
    }
    
    // Find matching variation based on selected attributes - Enhanced with decoding
    function findMatchingVariation() {
        // Get all selected attributes
        var selectedAttrs = {};
        var selectedCount = 0;
        
        $variationSelects.each(function() {
            var attrName = $(this).attr('name');
            var attrValue = $(this).val();
            
            if (attrValue && attrValue !== '') {
                // Store both raw and decoded values for comparison
                selectedAttrs[attrName] = {
                    raw: attrValue,
                    decoded: decodeAttributeValue(attrValue)
                };
                selectedCount++;
            }
        });
        
        // Log for debugging  
        console.log('RIDCOD: Selected attributes with decoding', selectedAttrs, 'Count:', selectedCount, 'Total selects:', $variationSelects.length);
        
        // Need all attributes selected to find a variation
        if (selectedCount !== $variationSelects.length) {
            console.log('RIDCOD: Not all attributes selected yet');
            return null;
        }
        
        // Find matching variation with improved logic and decoding support
        var bestMatch = null;
        var bestMatchScore = 0;
        
        for (var i = 0; i < variationsData.length; i++) {
            var variation = variationsData[i];
            var isMatch = true;
            var matchScore = 0;
            
            // Check if all selected attributes match this variation
            for (var attrName in selectedAttrs) {
                var selectedValue = selectedAttrs[attrName];
                var variationAttr = variation.attributes[attrName];
                
                // Handle case where attribute name might have different format
                if (!variationAttr) {
                    var cleanAttrName = attrName.replace('attribute_', '');
                    variationAttr = variation.attributes['attribute_' + cleanAttrName];
                }
                
                // Decode variation attribute value for comparison
                var decodedVariationAttr = decodeAttributeValue(variationAttr);
                
                console.log('RIDCOD: Checking attribute', attrName, 
                           'selected raw:', selectedValue.raw, 
                           'selected decoded:', selectedValue.decoded,
                           'variation raw:', variationAttr, 
                           'variation decoded:', decodedVariationAttr);
                
                // Check match with multiple comparison methods
                var isAttributeMatch = false;
                
                if (variationAttr === undefined || variationAttr === '') {
                    // Empty variation attribute means "any" value accepted
                    isAttributeMatch = true;
                    matchScore += 0.5;
                } else if (variationAttr === selectedValue.raw || 
                          decodedVariationAttr === selectedValue.decoded ||
                          decodedVariationAttr === selectedValue.raw ||
                          variationAttr === selectedValue.decoded) {
                    // Exact match in any form - increase score
                    isAttributeMatch = true;
                    matchScore++;
                }
                
                if (!isAttributeMatch) {
                    // No match found - this variation doesn't work
                    isMatch = false;
                    break;
                }
            }
            
            if (isMatch) {
                console.log('RIDCOD: Found matching variation with score', matchScore, variation);
                
                // Keep the best match (highest score)
                if (matchScore > bestMatchScore) {
                    bestMatch = variation;
                    bestMatchScore = matchScore;
                }
            }
        }
        
        if (bestMatch) {
            console.log('RIDCOD: Best matching variation found:', bestMatch);
            return bestMatch;
        }
        
        // No match found
        console.log('RIDCOD: No matching variation found for selections:', selectedAttrs);
        return null;
    }
    
    // New function for handling WooCommerce variation objects directly
    function updateVariationUI(variation) {
        if (!variation || !variation.variation_id) {
            // Reset form when no variation is selected
            updateUI(null);
            return;
        }
        
        console.log('RIDCOD: Updating UI with WooCommerce variation', variation);
        
        // Update hidden inputs
        $variationIdInput.val(variation.variation_id);
        $variationPriceInput.val(variation.display_price);
        
        // Update visible price
        if (variation.price_html) {
            $productPrice.html(variation.price_html);
        } else {
            $productPrice.html(formatPrice(variation.display_price));
        }
        
        // Update variation summary in details section
        var summaryHtml = '';
        for (var attribute in variation.attributes) {
            if (variation.attributes.hasOwnProperty(attribute)) {
                // Get attribute name without prefix
                var attrName = attribute.replace('attribute_', '');
                // Find the select element for this attribute
                var $select = $variationSelects.filter('[name="' + attribute + '"]');
                var label = $select.closest('.variation-row').find('label').text().replace(':', '');
                var selectedText = $select.find('option:selected').text();
                
                // Decode the selected text to handle Arabic characters properly
                var decodedText = decodeAttributeValue(selectedText);
                
                summaryHtml += '<span class="variation-detail">' + label + ': ' + decodedText + '</span><br>';
            }
        }
        $summaryVariationDetails.html(summaryHtml);
        
        // Update product image if variation has one
        if (variation.image && variation.image.src) {
            $productImage.attr('src', variation.image.src);
        }
        
        // Call external update functions if they exist
        if (typeof updateTotalPrice === 'function') {
            updateTotalPrice();
        }
    }
    
    // Original updateUI function - kept for backward compatibility
    function updateUI(variation) {
        if (!variation) {
            // Reset form when no variation is selected
            $variationIdInput.val('');
            $variationPriceInput.val('');
            $productPrice.html('اختر النوع');
            $summaryVariationDetails.html('');
            
            // Reset image to original
            if (originalImageSrc) {
                $productImage.attr('src', originalImageSrc);
            }
            return;
        }
        
        // Update hidden inputs
        $variationIdInput.val(variation.variation_id);
        $variationPriceInput.val(variation.display_price);
        
        // Update visible price
        if (variation.price_html) {
            $productPrice.html(variation.price_html);
        } else {
            $productPrice.html(formatPrice(variation.display_price));
        }
        
        // Update variation summary in details section
        var summaryHtml = '';
        $variationSelects.each(function() {
            var $select = $(this);
            var label = $select.closest('.variation-row').find('label').text().replace(':', '');
            var selectedText = $select.find('option:selected').text();
            
            // Decode the selected text to handle Arabic characters properly
            var decodedText = decodeAttributeValue(selectedText);
            
            summaryHtml += '<span class="variation-detail">' + label + ': ' + decodedText + '</span><br>';
        });
        $summaryVariationDetails.html(summaryHtml);
        
        // Update product image if variation has one
        if (variation.image && variation.image.src) {
            $productImage.attr('src', variation.image.src);
        }
        
        // Call external update functions if they exist
        if (typeof updateTotalPrice === 'function') {
            updateTotalPrice();
        }
    }
    
    // Initialize variation boxes to match any pre-selected options
    function initVariationBoxes() {
        console.log('RIDCOD: Initializing variation boxes and compatibility checking');
        
        // First, remove any existing disabled states
        $('.variation-boxes .variation-option').removeClass('disabled unavailable becoming-disabled becoming-enabled');
        $variationSelects.find('option').prop('disabled', false);
        
        $variationSelects.each(function() {
            var $select = $(this);
            var value = $select.val();
            var attributeName = $select.attr('name');
            
            if (value) {
                // Find and select the corresponding box
                $('.variation-boxes[data-attribute="' + attributeName + '"] .variation-option[data-value="' + value + '"]')
                    .addClass('selected');
            }
        });
        
        // Update available options based on any pre-selections
        updateAvailableOptions();
        
        // CRITICAL: Ensure first attribute is always enabled after initialization
        ensureFirstAttributeAlwaysEnabled();
        
        // Check if we have a valid variation already
        var variation = findMatchingVariation();
        if (variation) {
            updateUI(variation);
            $variationIdInput.val(variation.variation_id);
            $variationPriceInput.val(variation.display_price);
        }
    }
    
    // Call this function after any WooCommerce initialization
    setTimeout(function() {
        initVariationBoxes();
    }, 350);
    
    // Also call on window load to ensure everything is ready
    $(window).on('load', function() {
        setTimeout(function() {
            initVariationBoxes();
            console.log('RIDCOD: Re-initialized variation boxes after window load');
        }, 500);
    });
    
    // Since we're not using a nested form, we'll handle variation selection manually
    // and sync with the main form

    // Handle reset button
    $('.reset_variations').on('click', function(e) {
        e.preventDefault();
        
        console.log('RIDCOD: Resetting all variations');
        
        // Clear all selects
        $variationSelects.val('');
        
        // Deselect all box options and remove ALL disabled states
        $('.variation-boxes .variation-option').removeClass('selected disabled unavailable becoming-disabled becoming-enabled');
        
        // Re-enable all options in hidden selects
        $variationSelects.find('option').prop('disabled', false);
        
        // CRITICAL: Ensure first attribute remains fully enabled after reset
        ensureFirstAttributeAlwaysEnabled();
        
        // Update UI
        updateUI(null);
        
        // Clear hidden inputs
        $variationIdInput.val('');
        $variationPriceInput.val('');

        // Update WooCommerce hidden input too
        $('.variation_id').val('0');
        
        // Remove any messages
        $('.rid-global-message').remove();
        $('.rid-selection-cleared-message').remove();
        
        console.log('RIDCOD: All variations reset to initial state with first attribute protected');
    });
    
    // Handle box-style variation selection
    $('.variation-boxes').on('click', '.variation-option', function() {
        console.log('RIDCOD: Variation box clicked');
        
        var $option = $(this);
        var $boxesContainer = $option.closest('.variation-boxes');
        var attributeName = $boxesContainer.data('attribute');
        
        // Use the new function to get clean attribute value
        var value = getCleanAttributeValue($option);
        var rawValue = $option.data('value'); // Keep original value for select element
        
        console.log('RIDCOD: Selected value - Raw:', rawValue, 'Clean:', value);
        
        // Skip if this option is disabled
        if ($option.hasClass('disabled')) {
            console.log('RIDCOD: Skipping disabled option');
            return;
        }
        
        // Get the first attribute (primary selector) for special handling
        var $firstSelect = $variationSelects.first();
        var firstAttributeName = $firstSelect.attr('name');
        var isFirstAttribute = (attributeName === firstAttributeName);
        
        // Update the hidden select with the raw value (for form submission)
        var $select = $('select[name="' + attributeName + '"]');
        
        // Add visual feedback
        $select.addClass('changing');
        $option.addClass('selecting');
        
        // Deselect other options in this attribute group
        $boxesContainer.find('.variation-option').removeClass('selected');
        
        // Select this option
        $option.addClass('selected');
        
        // Set value in the hidden select (use raw value for compatibility) and trigger change
        $select.val(rawValue).trigger('change');
        
        setTimeout(function() {
            $select.removeClass('changing');
            $option.removeClass('selecting');
        }, 300);

        // IMPORTANT: If this is the first attribute, reset ALL other attributes
        // to show only compatible options
        if (isFirstAttribute) {
            console.log('RIDCOD: First attribute changed, resetting all other attributes');
            
            // Clear all other attribute selections
            $variationSelects.each(function() {
                var $otherSelect = $(this);
                var otherAttributeName = $otherSelect.attr('name');
                
                // Skip the attribute that was just changed
                if (otherAttributeName === attributeName) {
                    return;
                }
                
                // Clear the selection
                $otherSelect.val('');
                
                // Remove visual selection from boxes
                var $otherBoxesContainer = $('.variation-boxes[data-attribute="' + otherAttributeName + '"]');
                $otherBoxesContainer.find('.variation-option').removeClass('selected');
                
                console.log('RIDCOD: Cleared secondary attribute:', otherAttributeName);
            });
        }

        // Reset incompatible selections in other attributes (for non-first attributes)
        var hasIncompatibleReset = false;
        if (!isFirstAttribute) {
            hasIncompatibleReset = resetIncompatibleSelections(attributeName);
        }
        
        // Then update available options for all attributes
        updateAvailableOptions();

        // Find matching variation
        var variation = findMatchingVariation();
        if (variation) {
            updateUI(variation);

            // Sync with main form hidden inputs
            $variationIdInput.val(variation.variation_id);
            $variationPriceInput.val(variation.display_price);

            // Update WooCommerce hidden input too
            $('.variation_id').val(variation.variation_id);

            // Trigger total price update in main script
            if (typeof updateTotalPrice === 'function') {
                updateTotalPrice();
            }
            
            // Trigger custom event for price display update
            $(document).trigger('ridcod_variation_selected', [variation]);

            // Add subtle success animation to the variations container
            $('.rid-cod-variations').addClass('variation-updated');
            setTimeout(function() {
                $('.rid-cod-variations').removeClass('variation-updated');
            }, 600);

        } else {
            // No variation found, reset
            updateUI(null);
            $variationIdInput.val('');
            $variationPriceInput.val('');
            $('.variation_id').val('0');
            
            // Trigger custom event to reset price display to range
            $(document).trigger('ridcod_variation_reset');

            // Trigger total price update in main script
            if (typeof updateTotalPrice === 'function') {
                updateTotalPrice();
            }
        }
        
        // Show message if we reset any incompatible selections
        if (hasIncompatibleReset) {
            console.log('RIDCOD: Auto-cleared incompatible selections due to new choice');
        }
    });
    
    // Form validation - Enhanced validation for variation selection
    $form.on('submit', function(e) {
        console.log('RIDCOD: Form submitted - validating variations');
        
        // Only validate if we have variations
        if ($variationSelects.length > 0) {
            var allAttributesSelected = true;
            var missingAttributes = [];
            
            // First check if all variation selects have values
            $variationSelects.each(function() {
                var $select = $(this);
                var attrName = $select.attr('name');
                var attrValue = $select.val();
                
                if (!attrValue || attrValue === '') {
                    allAttributesSelected = false;
                    // Try to get decoded label from the select element first
                    var decodedLabel = $select.data('decoded-label');
                    if (!decodedLabel) {
                        // Fallback to getting from closest label and decoding
                        var label = $select.closest('.variation-row').find('label').text().replace(':', '').trim();
                        decodedLabel = decodeAttributeValue(label) || attrName.replace('attribute_', '');
                    }
                    missingAttributes.push(decodedLabel);
                }
            });
            
            // If not all attributes are selected, show specific error
            if (!allAttributesSelected) {
                e.preventDefault();
                
                var errorMessage = missingAttributes.length > 0 
                    ? 'الرجاء اختيار: ' + missingAttributes.join(', ')
                    : 'يرجى اختيار جميع خصائص المنتج';
                    
                alert(errorMessage);
                
                // Highlight missing selects
                $variationSelects.each(function() {
                    var $select = $(this);
                    var $container = $('.variation-boxes[data-attribute="' + $select.attr('name') + '"]');
                    
                    if (!$select.val()) {
                        $container.addClass('error-highlight');
                        setTimeout(function() {
                            $container.removeClass('error-highlight');
                        }, 3000);
                    }
                });
                
                // Scroll to variations section
                $('html, body').animate({
                    scrollTop: $wooVariationsForm.offset().top - 100
                }, 500);
                
                return false;
            }
            
            // Now check if we have a valid variation ID
            var currentVariationId = $variationIdInput.val();
            
            if (!currentVariationId || currentVariationId === '' || currentVariationId === '0') {
                console.log('RIDCOD: No variation ID found, attempting to find one...');
                
                // Try to find matching variation one more time
                var variation = findMatchingVariation();
                
                if (variation && variation.variation_id) {
                    console.log('RIDCOD: Found variation at last moment:', variation.variation_id);
                    $variationIdInput.val(variation.variation_id);
                    $variationPriceInput.val(variation.display_price);
                    updateUI(variation);
                } else {
                    // Still no variation found - this shouldn't happen if all attributes are selected
                    e.preventDefault();
                    
                    alert('حدث خطأ في تحديد نوع المنتج. الرجاء المحاولة مرة أخرى.');
                    
                    console.error('RIDCOD: All attributes selected but no matching variation found!');
                    console.log('Available variations:', variationsData);
                    
                    // Debug: show current selections
                    var currentSelections = {};
                    $variationSelects.each(function() {
                        currentSelections[$(this).attr('name')] = $(this).val();
                    });
                    console.log('Current selections:', currentSelections);
                    
                    return false;
                }
            }
            
            console.log('RIDCOD: Form validation passed. Variation ID:', $variationIdInput.val());
        }
        
        // If we reach here, validation passed or no variations needed
        return true;
    });
    
    // Handle variation select changes (for when the hidden select is updated programmatically)
    $variationSelects.on('change', function() {
        console.log('RIDCOD: Hidden select changed');

        var $select = $(this);
        var attributeName = $select.attr('name');
        var value = $select.val();
        
        // If this wasn't triggered by a box click, update the boxes
        if (!$select.hasClass('changing')) {
            // Deselect all options in this group
            $('.variation-boxes[data-attribute="' + attributeName + '"] .variation-option')
                .removeClass('selected');
                
            // Select the matching option if value exists
            if (value) {
                $('.variation-boxes[data-attribute="' + attributeName + '"] .variation-option[data-value="' + value + '"]')
                    .addClass('selected');
            }
        }

        // Reset incompatible selections in other attributes FIRST
        var hasIncompatibleReset = resetIncompatibleSelections(attributeName);
        
        // Then update available options for all attributes
        updateAvailableOptions();

        // Find matching variation and update UI
        var variation = findMatchingVariation();
        if (variation) {
            updateUI(variation);
            
            // Set values for form submission
            $variationIdInput.val(variation.variation_id);
            $variationPriceInput.val(variation.display_price);
            
            // Set WooCommerce variation ID for compatibility
            $('.variation_id').val(variation.variation_id);

            // Add visual feedback that variation was updated
            $('.rid-cod-variations').addClass('variation-updated');
            setTimeout(function() {
                $('.rid-cod-variations').removeClass('variation-updated');
            }, 600);

        } else {
            // No variation found, reset
            updateUI(null);
            $variationIdInput.val('');
            $variationPriceInput.val('');
            $('.variation_id').val('0');
            
            // Trigger custom event to reset price display to range
            $(document).trigger('ridcod_variation_reset');

            // Trigger total price update in main script
            if (typeof updateTotalPrice === 'function') {
                updateTotalPrice();
            }
        }
        
        // If we reset some incompatible selections, show a helpful message
        if (hasIncompatibleReset) {
            console.log('RIDCOD: Some selections were automatically cleared due to incompatibility');
        }
    });
    
    // Check for pre-selected values on page load
    setTimeout(function() {
        // First check if WooCommerce has already selected a variation
        var $wooVariationInput = $wooVariationsForm.find('input.variation_id');
        if ($wooVariationInput.length && $wooVariationInput.val() !== '' && $wooVariationInput.val() !== '0') {
            // WooCommerce has a variation selected - find and use it
            var wooVariationId = parseInt($wooVariationInput.val());
            for (var i = 0; i < variationsData.length; i++) {
                if (parseInt(variationsData[i].variation_id) === wooVariationId) {
                    updateUI(variationsData[i]);
                    return; // Exit early since we found a variation
                }
            }
        }
        
        // Check if URL contains variation data
        var urlParams = new URLSearchParams(window.location.search);
        if (urlParams.has('variation_id')) {
            var urlVariationId = parseInt(urlParams.get('variation_id'));
            for (var i = 0; i < variationsData.length; i++) {
                if (parseInt(variationsData[i].variation_id) === urlVariationId) {
                    // Pre-select attributes in dropdowns
                    var attrs = variationsData[i].attributes;
                    for (var attr in attrs) {
                        if (attrs.hasOwnProperty(attr) && attrs[attr] !== '') {
                            $variationSelects.filter('[name="' + attr + '"]').val(attrs[attr]);
                        }
                    }
                    // Update UI
                    updateUI(variationsData[i]);
                    return; // Exit early
                }
            }
        }
        
        // Fallback to our custom matching logic
        var variation = findMatchingVariation();
        if (variation) {
            updateUI(variation);
        }
    }, 300); // Increased timeout to allow WooCommerce to initialize

    // NEW: Function to provide user feedback when selections are auto-cleared
    function showGlobalSelectionMessage(message, type) {
        type = type || 'info'; // 'info', 'warning', 'success'
        
        // Remove any existing messages
        $('.rid-global-message').remove();
        
        var bgColor = type === 'warning' ? 'rgba(255, 193, 7, 0.95)' : 
                     type === 'success' ? 'rgba(40, 167, 69, 0.95)' : 
                     'rgba(23, 162, 184, 0.95)';
        
        var $message = $('<div class="rid-global-message">' + message + '</div>');
        $message.css({
            position: 'fixed',
            top: '20px',
            right: '20px',
            background: bgColor,
            color: 'white',
            padding: '12px 20px',
            borderRadius: '8px',
            fontSize: '14px',
            zIndex: 10000,
            boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
            border: '1px solid rgba(255,255,255,0.2)',
            opacity: 0,
            transform: 'translateX(100%)'
        });
        
        $('body').append($message);
        
        // Animate in
        $message.animate({
            opacity: 1,
            transform: 'translateX(0)'
        }, 300);
        
        // Auto-remove after 3 seconds
        setTimeout(function() {
            $message.animate({
                opacity: 0,
                transform: 'translateX(100%)'
            }, 300, function() {
                $message.remove();
            });
        }, 3000);
    }
    
    // Add instructional message for first attribute
    function addFirstAttributeInstructions() {
        // Function disabled - no instructions needed
        return;
    }

    // Initial call to add instructions
    addFirstAttributeInstructions();
});
